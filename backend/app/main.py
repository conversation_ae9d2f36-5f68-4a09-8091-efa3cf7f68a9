from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from app.core.config import settings
from app.api.api_v1.api import api_router
from app.db.database import get_supabase_client
from app.services.storage import StorageService
from app.core.auth import get_current_user
from app.middleware.rate_limiting import rate_limit_middleware
from app.middleware.monitoring import monitoring_middleware
from app.middleware.security import SecurityMiddleware
import os
from pathlib import Path


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    await StorageService.initialize_storage()
    yield
    # Shutdown (if needed)
    pass

app = FastAPI(
    title="Averum Contracts API",
    description="API for Averum Contracts Management System",
    version="1.0.0",
    openapi_url=f"{settings.API_PREFIX}/openapi.json",
    docs_url=f"{settings.API_PREFIX}/docs",
    redoc_url=f"{settings.API_PREFIX}/redoc",
    lifespan=lifespan,
)

# Set up CORS middleware
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Add security middleware (should be first for security headers)
app.add_middleware(SecurityMiddleware)

# Add monitoring middleware (should be second to track all requests)
app.middleware("http")(monitoring_middleware)

# Add rate limiting middleware
app.middleware("http")(rate_limit_middleware)

# Include API router
app.include_router(api_router, prefix=settings.API_PREFIX)

# Storage initialization is now handled in the lifespan handler above

# Serve static files (frontend) in production
if settings.ENVIRONMENT == "production":
    # Check if dist directory exists (built frontend)
    dist_path = Path(__file__).parent.parent.parent / "dist"
    if dist_path.exists():
        # Mount static files
        app.mount("/assets", StaticFiles(directory=str(dist_path / "assets")), name="assets")

        # Serve index.html for all non-API routes (SPA routing)
        @app.get("/{full_path:path}")
        async def serve_spa(full_path: str):
            # Don't serve SPA for API routes
            if full_path.startswith("api/"):
                return {"error": "API endpoint not found"}

            # Serve static files if they exist
            file_path = dist_path / full_path
            if file_path.exists() and file_path.is_file():
                return FileResponse(str(file_path))

            # Otherwise serve index.html (SPA routing)
            return FileResponse(str(dist_path / "index.html"))

@app.get("/")
async def root():
    if settings.ENVIRONMENT == "production":
        # In production, serve the frontend
        dist_path = Path(__file__).parent.parent.parent / "dist"
        if (dist_path / "index.html").exists():
            return FileResponse(str(dist_path / "index.html"))

    return {"message": "Welcome to Averum Contracts API. Visit /api/docs for documentation."}

@app.get("/api/auth/test")
async def test_auth(current_user: dict = Depends(get_current_user)):
    """
    Test endpoint to verify authentication is working.
    """
    return {
        "message": "Authentication successful!",
        "user": {
            "id": current_user.get("id"),
            "email": current_user.get("email", ""),
            "name": f"{current_user.get('first_name', '')} {current_user.get('last_name', '')}".strip(),
            "workspaces": current_user.get("workspaces", [])
        },
        "environment": settings.ENVIRONMENT,
        "clerk_configured": bool(settings.CLERK_SECRET_KEY and settings.CLERK_SECRET_KEY != "sk_test_placeholder_get_from_clerk_dashboard")
    }

@app.get("/api/health")
async def health_check():
    """
    Health check endpoint that verifies the database connection.
    This endpoint doesn't require authentication.
    """
    try:
        # Get Supabase client
        supabase = get_supabase_client()

        # Try a simple query to verify connection
        response = supabase.table("users").select("*").execute()

        # Check if we got a successful response
        if hasattr(response, 'data'):
            user_count = len(response.data)

            # Try to get some workspace data
            workspaces_response = supabase.table("workspaces").select("*").execute()
            workspace_count = len(workspaces_response.data) if hasattr(workspaces_response, 'data') else 0

            # Try to get some contract data
            contracts_response = supabase.table("contracts").select("*").execute()
            contract_count = len(contracts_response.data) if hasattr(contracts_response, 'data') else 0

            # Check storage status
            storage_status = "unknown"
            try:
                # List buckets to verify storage access
                supabase.storage.list_buckets()
                storage_status = "connected"
            except Exception as e:
                storage_status = f"error: {str(e)}"

            return {
                "status": "healthy",
                "database": "connected",
                "storage": storage_status,
                "database_url": settings.SUPABASE_URL,
                "counts": {
                    "users": user_count,
                    "workspaces": workspace_count,
                    "contracts": contract_count
                }
            }
        else:
            return {
                "status": "error",
                "message": "Database connection error",
                "details": "Invalid response from database"
            }
    except Exception as e:
        return {
            "status": "error",
            "message": "Database connection error",
            "details": str(e)
        }
