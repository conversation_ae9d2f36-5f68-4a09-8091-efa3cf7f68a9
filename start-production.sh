#!/bin/bash

# Production startup script for Railway deployment
set -e

echo "🚀 Starting Averum Contracts production deployment..."

# Set frontend environment variables for build
export VITE_API_BASE_URL="/api"
export VITE_CLERK_PUBLISHABLE_KEY=${CLERK_PUBLISHABLE_KEY:-"pk_test_ZGVmaW5pdGUtb3Bvc3N1bS0xLmNsZXJrLmFjY291bnRzLmRldiQ"}
export VITE_SUPABASE_URL=${SUPABASE_URL:-"https://kdcjdbufciuvvznqnotx.supabase.co"}
export VITE_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
export VITE_ENVIRONMENT=production
export VITE_USE_NEW_DOCUMENT_ENGINE=true

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
npm install

# Build frontend
echo "🔨 Building frontend..."
npm run build

# Install backend dependencies
echo "📦 Installing backend dependencies..."
cd backend
pip install -r requirements.txt

# Set environment variables for backend
export ENVIRONMENT=production
export API_PREFIX=/api
export SUPABASE_URL=${SUPABASE_URL:-"https://kdcjdbufciuvvznqnotx.supabase.co"}
export SUPABASE_KEY=${SUPABASE_KEY}
export CLERK_SECRET_KEY=${CLERK_SECRET_KEY}
export CLERK_PUBLISHABLE_KEY=${CLERK_PUBLISHABLE_KEY:-"pk_test_ZGVmaW5pdGUtb3Bvc3N1bS0xLmNsZXJrLmFjY291bnRzLmRldiQ"}
export BACKEND_CORS_ORIGINS='["*"]'
export PORT=${PORT:-8000}

echo "🌐 Starting backend server on port $PORT..."
echo "📁 Frontend built and will be served from /dist"

# Start the backend server (which now also serves the frontend)
python run.py
